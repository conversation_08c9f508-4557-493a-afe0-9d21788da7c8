# KontextFlux AI Image Generator

🎨 基于 KontextFlux API 的 AI 图像生成平台，提供美观的 Web 界面和 OpenAI 兼容的 API。

## ✨ 功能特性

### 🌐 Web 界面
- **现代化设计**：深色主题，毛玻璃效果
- **图片上传**：支持拖拽上传，多种格式（JPG, JPEG, PNG, WEBP）
- **宽高比选择**：支持 6 种宽高比
- **实时进度**：生成进度可视化显示
- **响应式布局**：支持桌面和移动设备

### 📡 API 支持
- **OpenAI 兼容**：完全兼容 OpenAI Chat Completions API
- **流式响应**：支持实时流式输出和进度显示
- **Web API**：专为 Web 界面优化的 API
- **健康检查**：系统状态监控
- **参数解析**：支持从prompt中解析size参数
- **水印去除**：自动去除生成图片的水印
- **代理支持**：支持HTTP代理配置

## 🚀 快速开始

### 1. 启动服务器
```bash
# 方式一：使用批处理脚本（Flask版本）
start.bat

# 方式二：直接运行 FastAPI 版本（推荐）
python kontextflux2api.py

# 方式三：直接运行 Flask 版本
python kontextfluxflaskapi.py
```

### 2. 访问界面
- **主界面**：http://127.0.0.1:8002/
- **API 测试工具**：http://127.0.0.1:8002/test

## 📐 支持的宽高比

| 宽高比 | 描述 | 适用场景 |
|--------|------|----------|
| `auto` | 自动 | 让 AI 自动选择最佳比例 |
| `1:1` | 正方形 | 头像、图标、社交媒体 |
| `2:3` | 竖屏 | 手机壁纸、海报 |
| `3:2` | 横屏 | 桌面壁纸、相片 |
| `9:16` | 手机竖屏 | 短视频、手机全屏 |
| `16:9` | 宽屏 | 视频封面、横幅 |

## 🔧 API 使用

### 配置信息
- **模型名称**：`kontext-flux`
- **API 密钥**：`sk-dummy-1f55e1bd4c3b4849bed71334258cbdf2`

### OpenAI 兼容 API

```bash
# 基础文本生成
curl -X POST http://127.0.0.1:8002/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-dummy-1f55e1bd4c3b4849bed71334258cbdf2" \
  -d '{
    "model": "kontext-flux",
    "messages": [
      {
        "role": "user",
        "content": "一只戴着VR眼镜的企鹅在酒吧里喝啤酒"
      }
    ],
    "size": "16:9",
    "stream": false
  }'

# 支持在prompt中指定尺寸参数
curl -X POST http://127.0.0.1:8002/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-dummy-1f55e1bd4c3b4849bed71334258cbdf2" \
  -d '{
    "model": "kontext-flux",
    "messages": [
      {
        "role": "user",
        "content": "一只戴着VR眼镜的企鹅在酒吧里喝啤酒 --size 2:3"
      }
    ],
    "stream": true
  }'
```

### 支持的尺寸参数
- `auto` - 自动选择最佳尺寸
- `1:1` - 正方形
- `2:3` - 竖屏
- `3:2` - 横屏

## 🌐 V2Ray代理配置

项目已集成V2Ray代理支持，可通过`proxy_config.py`文件进行配置：

### 配置步骤

1. **确保V2Ray客户端运行**
   ```bash
   # 检查V2Ray是否运行
   netstat -an | findstr :10808  # SOCKS5端口
   netstat -an | findstr :8080   # HTTP端口
   ```

2. **修改代理配置**
   ```python
   # 编辑 proxy_config.py
   ENABLE_PROXY = True          # 启用代理
   PROXY_TYPE = "socks5"        # 代理类型：socks5 或 http
   V2RAY_SOCKS5_PORT = 10808    # V2Ray SOCKS5端口
   V2RAY_HTTP_PORT = 8080       # V2Ray HTTP端口
   ```

3. **常见V2Ray端口配置**
   - **SOCKS5代理**: `127.0.0.1:10808` (推荐)
   - **HTTP代理**: `127.0.0.1:8080`

### 代理测试
```bash
# 测试SOCKS5代理
curl --proxy socks5://127.0.0.1:10808 https://www.google.com

# 测试HTTP代理
curl --proxy http://127.0.0.1:8080 https://www.google.com
```

### Web API

```bash
curl -X POST http://127.0.0.1:8002/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "一只戴着VR眼镜的企鹅在酒吧里喝啤酒",
    "aspect_ratio": "16:9"
  }'
```

## 📡 API 端点

### Web 界面
- `GET /` - 主界面
- `GET /test` - API 测试工具

### API 端点
- `GET /api` - API 信息
- `GET /health` - 健康检查
- `POST /api/generate` - Web 生成 API
- `GET /api/status/<task_id>` - 任务状态查询

### OpenAI 兼容
- `GET /v1/models` - 获取模型列表
- `POST /v1/chat/completions` - 聊天完成 API

## 📁 项目结构

```
kontextflux2api/
├── kontextflux2api.py         # FastAPI服务器（推荐）
├── kontextfluxflaskapi.py     # Flask服务器（备用）
├── proxy_config.py            # V2Ray代理配置
├── templates/
│   ├── index.html             # 主界面
│   └── api_test.html          # API 测试工具
├── client_api_keys.json       # API密钥配置
├── start.bat                  # 启动脚本
├── requirements.txt           # 依赖列表
└── README.md                  # 说明文档
```

## 🛠️ 技术栈

- **后端**：FastAPI + Flask, Python 3.7+
- **前端**：HTML5, CSS3, JavaScript (ES6+)
- **API**：KontextFlux API
- **加密**：PyCryptodome
- **网络**：HTTPX, WebSocket, aiohttp
- **代理支持**：V2Ray SOCKS5/HTTP代理
- **水印去除**：WatermarkRemover.io API

## 📝 使用说明

1. **图像生成**：在主界面输入描述，选择宽高比，点击生成
2. **图片上传**：可上传参考图片进行图像编辑
3. **API 测试**：使用测试工具验证 API 功能
4. **流式响应**：支持实时查看生成进度

## 🔒 安全说明

- API 密钥已预配置，仅用于演示
- 生产环境请更换为实际的 API 密钥
- 建议使用 HTTPS 和适当的身份验证

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 检查服务器日志获取错误信息
- 使用 API 测试工具验证功能
- 确保网络连接正常

---

🎉 **享受 AI 图像生成的乐趣！**
