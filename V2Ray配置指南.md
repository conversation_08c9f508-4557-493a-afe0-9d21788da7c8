# V2Ray配置指南

## 📋 概述

本项目已集成V2Ray代理支持，可以通过V2Ray客户端访问KontextFlux API和水印去除服务。

## 🔧 V2Ray客户端配置

### 1. 确保V2Ray客户端运行

确保你的V2Ray客户端正在运行，并且配置了本地代理端口。

### 2. 常见V2Ray客户端配置

#### Windows客户端 (v2rayN)
1. 打开v2rayN客户端
2. 确保服务器配置正确并已连接
3. 查看"参数设置" → "入站连接设置"
4. 默认配置通常为：
   - **SOCKS端口**: 10808
   - **HTTP端口**: 8080

#### macOS客户端 (V2rayU)
1. 打开V2rayU客户端
2. 确保服务器配置正确并已连接
3. 查看"偏好设置" → "高级"
4. 默认配置通常为：
   - **SOCKS端口**: 10808
   - **HTTP端口**: 8080

#### Linux客户端
1. 确保v2ray服务正在运行
2. 检查配置文件中的入站连接设置
3. 默认配置通常为：
   - **SOCKS端口**: 10808
   - **HTTP端口**: 8080

## ⚙️ 项目代理配置

### 修改 `proxy_config.py`

```python
# V2Ray代理配置文件
ENABLE_PROXY = True          # 启用代理
PROXY_TYPE = "socks5"        # 代理类型：socks5 或 http

# V2Ray SOCKS5代理配置（推荐）
V2RAY_SOCKS5_HOST = "127.0.0.1"
V2RAY_SOCKS5_PORT = 10808

# V2Ray HTTP代理配置（备选）
V2RAY_HTTP_HOST = "127.0.0.1"
V2RAY_HTTP_PORT = 8080
```

### 配置选项说明

- **ENABLE_PROXY**: 是否启用代理（True/False）
- **PROXY_TYPE**: 代理类型
  - `"socks5"` - 推荐，性能更好
  - `"http"` - 备选方案
- **端口配置**: 根据你的V2Ray客户端设置调整

## 🧪 测试代理连接

### 1. 运行代理测试脚本
```bash
python test_proxy.py
```

### 2. 手动测试代理
```bash
# 测试SOCKS5代理
curl --proxy socks5://127.0.0.1:10808 https://httpbin.org/ip

# 测试HTTP代理
curl --proxy http://127.0.0.1:8080 https://httpbin.org/ip
```

### 3. 检查端口状态
```bash
# Windows
netstat -an | findstr :10808
netstat -an | findstr :8080

# Linux/macOS
netstat -an | grep :10808
netstat -an | grep :8080
```

## 🚨 常见问题

### 1. 代理连接失败
- **检查V2Ray客户端是否运行**
- **确认端口配置正确**
- **检查防火墙设置**

### 2. 端口被占用
```bash
# 查看端口占用情况
netstat -ano | findstr :10808

# 如果需要，可以修改proxy_config.py中的端口
```

### 3. 代理速度慢
- **尝试切换到HTTP代理**
- **检查V2Ray服务器连接质量**
- **优化V2Ray客户端配置**

## 🔄 禁用代理

如果不需要使用代理，可以在 `proxy_config.py` 中设置：

```python
ENABLE_PROXY = False
```

或者直接修改 `kontextflux2api.py` 中的代理配置：

```python
proxies = {"http": None, "https": None}
```

## 📝 注意事项

1. **确保V2Ray客户端稳定运行**
2. **定期检查代理连接状态**
3. **根据网络环境选择合适的代理类型**
4. **保持V2Ray客户端和服务器配置同步**

## 🆘 获取帮助

如果遇到问题：
1. 运行 `python test_proxy.py` 进行诊断
2. 检查V2Ray客户端日志
3. 确认网络连接正常
4. 验证服务器配置正确
