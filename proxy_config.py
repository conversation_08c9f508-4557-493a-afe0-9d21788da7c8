# V2Ray代理配置文件
# 根据你的V2Ray配置修改以下设置

# V2Ray SOCKS5代理配置（推荐）
V2RAY_SOCKS5_HOST = "127.0.0.1"
V2RAY_SOCKS5_PORT = 10808

# V2Ray HTTP代理配置（备选）
V2RAY_HTTP_HOST = "127.0.0.1"
V2RAY_HTTP_PORT = 8080

# 代理类型选择：'socks5' 或 'http'
PROXY_TYPE = "socks5"

# 是否启用代理
ENABLE_PROXY = True

def get_proxy_config():
    """获取代理配置"""
    if not ENABLE_PROXY:
        return {
            "http": None,
            "https": None,
        }
    
    if PROXY_TYPE == "socks5":
        proxy_url = f"socks5://{V2RAY_SOCKS5_HOST}:{V2RAY_SOCKS5_PORT}"
    elif PROXY_TYPE == "http":
        proxy_url = f"http://{V2RAY_HTTP_HOST}:{V2RAY_HTTP_PORT}"
    else:
        raise ValueError(f"不支持的代理类型: {PROXY_TYPE}")
    
    return {
        "http": proxy_url,
        "https": proxy_url,
    }

# 常见V2Ray配置说明：
"""
V2Ray客户端常见配置：

1. SOCKS5代理（默认）：
   - 端口：10808
   - 协议：SOCKS5
   - 地址：127.0.0.1

2. HTTP代理：
   - 端口：8080
   - 协议：HTTP
   - 地址：127.0.0.1

3. 如何查看V2Ray配置：
   - 查看V2Ray客户端的入站连接设置
   - 通常在"参数设置"或"入站连接"中可以找到
   - 确保V2Ray客户端正在运行

4. 测试代理是否工作：
   - 可以使用curl测试：
     curl --proxy socks5://127.0.0.1:10808 https://www.google.com
   - 或者：
     curl --proxy http://127.0.0.1:8080 https://www.google.com
"""
