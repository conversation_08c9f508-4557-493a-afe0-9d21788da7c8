<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KontextFlux AI Image Generator</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 350px 1fr 350px;
            gap: 30px;
            min-height: 100vh;
        }

        .panel {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .upload-panel {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .upload-area {
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            padding: 40px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-area:hover {
            border-color: #6366f1;
            background: rgba(99, 102, 241, 0.1);
        }

        .upload-area.dragover {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }

        .upload-icon {
            font-size: 48px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 10px;
        }

        .upload-hint {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
        }

        .uploaded-image {
            max-width: 100%;
            max-height: 200px;
            width: auto;
            height: auto;
            object-fit: contain;
            border-radius: 10px;
            margin-top: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .upload-preview {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 120px;
            justify-content: center;
        }

        .aspect-ratio-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 20px;
        }

        .aspect-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 12px 8px;
            color: #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 12px;
        }

        .aspect-btn:hover {
            background: rgba(99, 102, 241, 0.3);
            border-color: #6366f1;
        }

        .aspect-btn.active {
            background: #6366f1;
            border-color: #6366f1;
        }

        .prompt-area {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            color: #ffffff;
            font-size: 14px;
            resize: vertical;
            min-height: 120px;
            width: 100%;
            font-family: inherit;
        }

        .prompt-area:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .generate-btn {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            color: #ffffff;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result-panel {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
        }

        .sample-image {
            max-width: 100%;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .result-placeholder {
            color: rgba(255, 255, 255, 0.5);
            font-size: 18px;
        }

        .tasks-panel h3 {
            color: #ffffff;
            margin-bottom: 20px;
            font-size: 18px;
        }

        .no-tasks {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 20px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #6366f1, #8b5cf6);
            width: 0%;
            transition: width 0.3s ease;
        }

        .loading-text {
            margin-top: 10px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }

        #file-input {
            display: none;
        }

        @media (max-width: 1200px) {
            .container {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            grid-column: 1 / -1;
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.1rem;
        }

        /* 批量生成相关样式 */
        .batch-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .batch-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            font-weight: 500;
        }

        .quantity-selector {
            display: flex;
            gap: 8px;
        }

        .quantity-btn {
            width: 35px;
            height: 35px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 600;
        }

        .quantity-btn:hover {
            background: rgba(99, 102, 241, 0.3);
            border-color: #6366f1;
        }

        .quantity-btn.active {
            background: #6366f1;
            border-color: #6366f1;
        }

        /* 进度显示样式 */
        .progress-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            z-index: 1000;
            display: none;
            justify-content: center;
            align-items: center;
        }

        .progress-container {
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            min-width: 400px;
            max-width: 500px;
            text-align: center;
            position: relative;
        }

        .progress-title {
            color: #ffffff;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .progress-bar-container {
            width: 100%;
            height: 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 15px;
            position: relative;
        }

        .progress-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, #6366f1, #8b5cf6);
            width: 0%;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-bar-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.6);
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .progress-close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
        }

        .progress-info {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            margin-bottom: 10px;
        }

        .progress-queue {
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
        }

        /* 历史记录样式 */
        .history-panel {
            max-height: 70vh;
            overflow-y: auto;
        }

        .history-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .history-btn {
            flex: 1;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #ffffff;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .history-btn:hover {
            background: rgba(99, 102, 241, 0.3);
            border-color: #6366f1;
        }

        .history-btn.danger:hover {
            background: rgba(239, 68, 68, 0.3);
            border-color: #ef4444;
        }

        .history-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 12px;
            margin-bottom: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .history-item:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .history-item.selected {
            border-color: #6366f1;
            background: rgba(99, 102, 241, 0.1);
        }

        .history-image {
            width: 100%;
            max-height: 120px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
        }

        .history-info {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .history-actions {
            display: flex;
            gap: 5px;
        }

        .history-action-btn {
            flex: 1;
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: #ffffff;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .history-action-btn:hover {
            background: rgba(99, 102, 241, 0.3);
            border-color: #6366f1;
        }

        .history-action-btn.danger:hover {
            background: rgba(239, 68, 68, 0.3);
            border-color: #ef4444;
        }

        .history-checkbox {
            margin-right: 8px;
        }

        /* 结果网格样式 */
        .result-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .result-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .result-image {
            width: 100%;
            max-height: 200px;
            object-fit: contain;
            border-radius: 10px;
            margin-bottom: 10px;
        }

        .result-actions {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .result-btn {
            padding: 6px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: #ffffff;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .result-btn:hover {
            background: rgba(99, 102, 241, 0.3);
            border-color: #6366f1;
        }

        /* 生成状态提示样式 */
        .status-info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: #60a5fa;
        }

        .status-success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #4ade80;
        }

        .status-warning {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.3);
            color: #fbbf24;
        }

        .status-error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #f87171;
        }

        .status-processing {
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid rgba(139, 92, 246, 0.3);
            color: #a78bfa;
        }

        /* 图片预览模态框样式 */
        .image-preview-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            z-index: 2000;
            display: none;
            justify-content: center;
            align-items: center;
            cursor: pointer;
        }

        .image-preview-container {
            position: relative;
            max-width: 90vw;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .image-preview-img {
            max-width: 100%;
            max-height: 80vh;
            object-fit: contain;
            border-radius: 10px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            cursor: default;
        }

        .image-preview-controls {
            margin-top: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .image-preview-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #ffffff;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .image-preview-btn:hover {
            background: rgba(99, 102, 241, 0.3);
            border-color: #6366f1;
        }

        .image-preview-close {
            position: absolute;
            top: -50px;
            right: 0;
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.8);
            font-size: 24px;
            cursor: pointer;
            padding: 10px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .image-preview-close:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
        }

        .image-preview-info {
            margin-top: 15px;
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            max-width: 600px;
        }

        .image-preview-zoom {
            cursor: zoom-in;
        }

        .image-preview-zoom.zoomed {
            cursor: zoom-out;
            transform: scale(1.5);
            transition: transform 0.3s ease;
        }

        /* 多张图片预览样式 */
        .uploaded-images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 10px;
            max-height: 300px;
            overflow-y: auto;
        }

        .uploaded-image-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .uploaded-image-item:hover {
            border-color: #6366f1;
            transform: translateY(-2px);
        }

        .uploaded-image-item img {
            width: 100%;
            height: 80px;
            object-fit: cover;
            cursor: pointer;
        }

        .uploaded-image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .uploaded-image-item:hover .uploaded-image-overlay {
            opacity: 1;
        }

        .uploaded-image-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            color: white;
            padding: 4px 8px;
            margin: 0 2px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .uploaded-image-btn:hover {
            background: rgba(239, 68, 68, 0.3);
            border-color: #f87171;
        }

        .uploaded-image-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            color: white;
            padding: 8px 6px 4px;
            font-size: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-magic"></i> KontextFlux AI</h1>
            <p>AI驱动的图像生成与编辑平台</p>
        </div>

        <!-- 上传和控制面板 -->
        <div class="panel upload-panel">
            <div class="upload-area" onclick="document.getElementById('file-input').click()">
                <div class="upload-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <div class="upload-text">点击上传图片</div>
                <div class="upload-hint">或拖拽图片到此处<br>支持 JPG, JPEG, PNG, WEBP 格式，可上传多张</div>
                <input type="file" id="file-input" accept="image/*" multiple>
            </div>

            <!-- 已上传图片预览区域 -->
            <div id="uploaded-images-container" style="display: none; margin-top: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <span style="color: rgba(255,255,255,0.8); font-weight: 500;">已上传图片 (<span id="image-count">0</span>)</span>
                    <button onclick="clearAllImages()" style="background: none; border: 1px solid rgba(239, 68, 68, 0.3); color: #f87171; padding: 5px 10px; border-radius: 5px; font-size: 12px; cursor: pointer;">
                        清空全部
                    </button>
                </div>
                <div id="uploaded-images-grid" class="uploaded-images-grid"></div>
            </div>

            <div class="aspect-ratio-buttons">
                <button class="aspect-btn active" data-ratio="auto">
                    <i class="fas fa-expand-arrows-alt"></i><br>auto
                </button>
                <button class="aspect-btn" data-ratio="1:1">
                    <i class="fas fa-square"></i><br>1:1
                </button>
                <button class="aspect-btn" data-ratio="2:3">
                    <i class="fas fa-mobile-alt"></i><br>2:3
                </button>
                <button class="aspect-btn" data-ratio="3:2">
                    <i class="fas fa-desktop"></i><br>3:2
                </button>
                <button class="aspect-btn" data-ratio="9:16">
                    <i class="fas fa-mobile"></i><br>9:16
                </button>
                <button class="aspect-btn" data-ratio="16:9">
                    <i class="fas fa-tv"></i><br>16:9
                </button>
            </div>

            <textarea class="prompt-area" placeholder="描述你想要生成或编辑的图像...&#10;例如：一只戴着VR眼镜的企鹅在酒吧里喝啤酒"></textarea>

            <div class="batch-controls">
                <span class="batch-label">生成数量:</span>
                <div class="quantity-selector">
                    <button class="quantity-btn active" data-quantity="1">1</button>
                    <button class="quantity-btn" data-quantity="2">2</button>
                    <button class="quantity-btn" data-quantity="3">3</button>
                    <button class="quantity-btn" data-quantity="4">4</button>
                </div>
            </div>

            <button class="generate-btn" onclick="generateImages()">
                <i class="fas fa-magic"></i> 开始生成
            </button>

            <!-- 生成状态提示区域 -->
            <div id="generation-status" style="display: none; margin-top: 15px; padding: 12px; border-radius: 10px; font-size: 14px; text-align: center;">
                <div id="status-icon" style="font-size: 18px; margin-bottom: 5px;"></div>
                <div id="status-text"></div>
                <div id="status-details" style="font-size: 12px; margin-top: 5px; opacity: 0.8;"></div>
            </div>

            <!-- 内联进度条 -->
            <div id="inline-progress" style="display: none; margin-top: 15px; padding: 15px; border-radius: 10px; background: rgba(139, 92, 246, 0.1); border: 1px solid rgba(139, 92, 246, 0.3);">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <div id="progress-text" style="color: #a78bfa; font-weight: 500;">正在生成图像...</div>
                    <button id="cancel-btn" onclick="cancelGeneration()" style="background: none; border: 1px solid rgba(239, 68, 68, 0.3); color: #f87171; padding: 5px 10px; border-radius: 5px; font-size: 12px; cursor: pointer;">
                        取消
                    </button>
                </div>
                <div style="background: rgba(139, 92, 246, 0.2); border-radius: 10px; height: 8px; overflow: hidden; margin-bottom: 8px;">
                    <div id="progress-fill" style="background: linear-gradient(90deg, #8b5cf6, #a78bfa); height: 100%; width: 0%; transition: width 0.3s ease; border-radius: 10px;"></div>
                </div>
                <div id="progress-details" style="color: rgba(167, 139, 250, 0.8); font-size: 12px; text-align: center;"></div>
            </div>
        </div>

        <!-- 结果显示面板 -->
        <div class="panel result-panel" style="position: relative;">
            <div id="result-content">
                <div class="result-placeholder">
                    <i class="fas fa-image" style="font-size: 48px; color: rgba(255,255,255,0.3); margin-bottom: 15px;"></i>
                    <p>生成的图像将在这里显示</p>
                </div>
            </div>

            <!-- 生成中的居中提示 -->
            <div id="generation-center-status" style="display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; z-index: 100; background: rgba(30, 41, 59, 0.95); padding: 30px; border-radius: 15px; border: 1px solid rgba(99, 102, 241, 0.3); backdrop-filter: blur(10px); min-width: 300px; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
                <div id="center-status-icon" style="font-size: 24px; margin-bottom: 15px; color: #a78bfa;"></div>
                <div id="center-status-text" style="color: #ffffff; font-weight: 500; margin-bottom: 10px; font-size: 16px;"></div>
                <div id="center-status-details" style="color: rgba(255,255,255,0.7); font-size: 14px; margin-bottom: 15px;"></div>
                <div style="background: rgba(139, 92, 246, 0.2); border-radius: 10px; height: 6px; overflow: hidden; margin-bottom: 10px;">
                    <div id="center-progress-fill" style="background: linear-gradient(90deg, #8b5cf6, #a78bfa); height: 100%; width: 0%; transition: width 0.3s ease; border-radius: 10px;"></div>
                </div>
                <button onclick="cancelGeneration()" style="background: rgba(239, 68, 68, 0.2); border: 1px solid rgba(239, 68, 68, 0.3); color: #f87171; padding: 8px 16px; border-radius: 6px; font-size: 12px; cursor: pointer; margin-top: 5px; transition: all 0.3s ease;">
                    取消生成
                </button>
            </div>
        </div>

        <!-- 历史记录和API信息面板 -->
        <div class="panel history-panel">
            <!-- API信息区域 -->
            <div style="margin-bottom: 20px; padding: 15px; background: rgba(99, 102, 241, 0.1); border-radius: 10px; border: 1px solid rgba(99, 102, 241, 0.3);">
                <h3 style="color: #ffffff; margin-bottom: 10px; font-size: 16px;">
                    <i class="fas fa-key"></i> API 信息
                </h3>
                <p style="color: rgba(255,255,255,0.8); font-size: 12px; margin: 5px 0;">
                    <strong>模型:</strong> kontext-flux
                </p>
                <p style="color: rgba(255,255,255,0.8); font-size: 12px; margin: 5px 0;">
                    <strong>密钥:</strong> sk-dummy-1f55e1bd4c3b4849bed71334258cbdf2
                </p>
                <p style="color: rgba(255,255,255,0.7); font-size: 11px; margin: 10px 0 5px 0; text-align: center;">
                    <a href="/test" style="color: #6366f1; text-decoration: none; font-weight: 500;">
                        <i class="fas fa-code"></i> API 测试工具
                    </a>
                </p>
                <details style="margin-top: 10px;">
                    <summary style="color: rgba(255,255,255,0.7); font-size: 11px; cursor: pointer; margin-bottom: 8px;">
                        <i class="fas fa-info-circle"></i> API 使用说明
                    </summary>
                    <div style="font-size: 10px; color: rgba(255,255,255,0.6); line-height: 1.4;">
                        <p><strong>OpenAI兼容:</strong> POST /v1/chat/completions</p>
                        <p><strong>Web API:</strong> POST /api/generate</p>
                        <p><strong>支持宽高比:</strong> auto, 1:1, 2:3, 3:2, 9:16, 16:9</p>
                    </div>
                </details>
            </div>

            <!-- 历史记录区域 -->
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 16px;">
                <i class="fas fa-history"></i> 历史记录 (<span id="history-count">0</span>)
            </h3>

            <div class="history-controls">
                <button class="history-btn" onclick="selectAllHistory()">
                    <i class="fas fa-check-square"></i> 全选
                </button>
                <button class="history-btn" onclick="downloadSelected()">
                    <i class="fas fa-download"></i> 下载
                </button>
                <button class="history-btn danger" onclick="deleteSelected()">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>

            <div id="history-list">
                <div class="no-tasks">
                    <i class="fas fa-image" style="font-size: 24px; margin-bottom: 10px; opacity: 0.5;"></i>
                    <p>暂无历史记录</p>
                </div>
            </div>
        </div>

        <!-- 进度显示模态框 -->
        <div class="progress-overlay" id="progress-modal" onclick="handleProgressOverlayClick(event)">
            <div class="progress-container" onclick="event.stopPropagation()">
                <button class="progress-close-btn" onclick="cancelGeneration()" title="取消生成">
                    <i class="fas fa-times"></i>
                </button>
                <div class="progress-title">正在生成图像</div>
                <div class="progress-bar-container">
                    <div class="progress-bar-fill" id="modal-progress-fill"></div>
                </div>
                <div class="progress-info" id="modal-progress-info">准备中...</div>
                <div class="progress-queue" id="modal-progress-queue"></div>
            </div>
        </div>

        <!-- 图片预览模态框 -->
        <div class="image-preview-overlay" id="image-preview-modal" onclick="closeImagePreview()">
            <div class="image-preview-container" onclick="event.stopPropagation()">
                <button class="image-preview-close" onclick="closeImagePreview()" title="关闭预览">
                    <i class="fas fa-times"></i>
                </button>
                <img id="preview-image" class="image-preview-img image-preview-zoom" onclick="toggleImageZoom()" alt="图片预览">
                <div class="image-preview-info" id="preview-info"></div>
                <div class="image-preview-controls">
                    <button class="image-preview-btn" onclick="downloadPreviewImage()">
                        <i class="fas fa-download"></i> 下载图片
                    </button>
                    <button class="image-preview-btn" onclick="openImageInNewTab()">
                        <i class="fas fa-external-link-alt"></i> 新窗口打开
                    </button>
                    <button class="image-preview-btn" onclick="copyImageUrl()">
                        <i class="fas fa-copy"></i> 复制链接
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedRatio = 'auto';
        let selectedQuantity = 1;
        let uploadedFiles = [];
        let uploadedImageUrls = []; // 新增：存储上传图片的base64 URL
        let generationHistory = JSON.parse(localStorage.getItem('generationHistory') || '[]');
        let generationQueue = [];
        let isGenerating = false;
        let currentGenerationController = null; // 用于取消生成

        // 工具函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateHistoryDisplay();

            // 添加提示词输入监听
            const promptArea = document.querySelector('.prompt-area');
            let promptTimeout;

            promptArea.addEventListener('input', function() {
                clearTimeout(promptTimeout);
                promptTimeout = setTimeout(() => {
                    const prompt = this.value.trim();
                    const hasImages = uploadedImageUrls.length > 0;

                    if (prompt && hasImages) {
                        showStatus('info', '🎨', '图片与提示词已关联',
                            `将基于${uploadedImageUrls.length}张上传图片和提示词进行创作`);
                        setTimeout(hideStatus, 2000);
                    } else if (prompt && !hasImages) {
                        hideStatus(); // 隐藏之前的提示
                    }
                }, 1000); // 1秒后显示提示
            });

            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                // Ctrl+Enter 快速生成
                if (e.ctrlKey && e.key === 'Enter') {
                    e.preventDefault();
                    if (!isGenerating) {
                        generateImages();
                    }
                }
                // Esc 取消生成
                if (e.key === 'Escape' && isGenerating) {
                    e.preventDefault();
                    if (confirm('确定要取消生成吗？')) {
                        cancelGeneration();
                    }
                }
            });
        });

        // 宽高比选择
        document.querySelectorAll('.aspect-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.aspect-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                selectedRatio = this.dataset.ratio;
            });
        });

        // 数量选择
        document.querySelectorAll('.quantity-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.quantity-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                selectedQuantity = parseInt(this.dataset.quantity);
            });
        });

        // 文件上传处理
        const uploadArea = document.querySelector('.upload-area');
        const fileInput = document.getElementById('file-input');

        fileInput.addEventListener('change', handleFiles);

        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            handleFiles({ target: { files } });
        });

        function handleFiles(event) {
            const files = Array.from(event.target.files);

            if (files.length > 0) {
                // 添加新文件到现有列表
                files.forEach(file => {
                    if (file.size > 10 * 1024 * 1024) { // 10MB限制
                        showStatus('error', '❌', '文件过大', `${file.name} 超过10MB限制`);
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        // 添加到图片URL列表
                        uploadedImageUrls.push({
                            url: e.target.result,
                            name: file.name,
                            size: file.size,
                            id: Date.now() + Math.random()
                        });

                        updateUploadedImagesDisplay();
                    };
                    reader.readAsDataURL(file);
                });

                // 显示上传成功提示
                const message = files.length === 1
                    ? '图片上传成功'
                    : `${files.length}张图片上传成功`;
                showStatus('success', '📷', message, '图片已准备好与提示词结合生成');
                setTimeout(hideStatus, 3000);
            }

            // 清空文件输入，允许重复选择相同文件
            event.target.value = '';
        }

        function updateUploadedImagesDisplay() {
            const container = document.getElementById('uploaded-images-container');
            const grid = document.getElementById('uploaded-images-grid');
            const count = document.getElementById('image-count');

            if (uploadedImageUrls.length === 0) {
                container.style.display = 'none';
                return;
            }

            container.style.display = 'block';
            count.textContent = uploadedImageUrls.length;

            grid.innerHTML = uploadedImageUrls.map((img, index) => `
                <div class="uploaded-image-item">
                    <img src="${img.url}" alt="${img.name}" onclick="viewImage('${img.url}', '上传的参考图片: ${img.name}', '')">
                    <div class="uploaded-image-overlay">
                        <button class="uploaded-image-btn" onclick="removeImage(${index})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="uploaded-image-btn" onclick="replaceImage(${index})" title="更换">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="uploaded-image-info">
                        ${img.name.length > 15 ? img.name.substring(0, 12) + '...' : img.name}
                    </div>
                </div>
            `).join('');
        }

        function removeImage(index) {
            uploadedImageUrls.splice(index, 1);
            updateUploadedImagesDisplay();

            showStatus('info', '🗑️', '图片已删除', `已从上传列表中移除图片`);
            setTimeout(hideStatus, 2000);
        }

        function replaceImage(index) {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    if (file.size > 10 * 1024 * 1024) {
                        showStatus('error', '❌', '文件过大', `${file.name} 超过10MB限制`);
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        uploadedImageUrls[index] = {
                            url: e.target.result,
                            name: file.name,
                            size: file.size,
                            id: Date.now() + Math.random()
                        };
                        updateUploadedImagesDisplay();

                        showStatus('success', '🔄', '图片已更换', `已替换为 ${file.name}`);
                        setTimeout(hideStatus, 2000);
                    };
                    reader.readAsDataURL(file);
                }
            };
            input.click();
        }

        function clearAllImages() {
            if (uploadedImageUrls.length === 0) return;

            if (confirm(`确定要清空所有 ${uploadedImageUrls.length} 张上传的图片吗？`)) {
                uploadedImageUrls = [];
                updateUploadedImagesDisplay();

                showStatus('info', '🧹', '已清空所有图片', '所有上传的图片已被移除');
                setTimeout(hideStatus, 2000);
            }
        }

        async function generateImages() {
            const prompt = document.querySelector('.prompt-area').value;

            if (!prompt.trim() && uploadedImageUrls.length === 0) {
                showStatus('error', '❌', '请输入图像描述或上传图片', '至少需要提供提示词或上传图片中的一种');
                return;
            }

            if (isGenerating) {
                showStatus('warning', '⏳', '正在生成中', '请等待当前任务完成或取消后再试');
                return;
            }

            // 显示图片关联信息
            showImageAssociationInfo();

            // 创建生成队列
            generationQueue = [];
            for (let i = 0; i < selectedQuantity; i++) {
                generationQueue.push({
                    id: Date.now() + i,
                    prompt: prompt,
                    aspect_ratio: selectedRatio,
                    status: 'pending',
                    index: i + 1
                });
            }

            // 创建取消控制器
            currentGenerationController = new AbortController();

            isGenerating = true;
            document.querySelector('.generate-btn').disabled = true;

            // 显示包含上传图片的提示
            const hasImages = uploadedImageUrls.length > 0;
            const initialMessage = hasImages
                ? `准备生成 ${selectedQuantity} 张图片（包含 ${uploadedImageUrls.length} 张上传图片）`
                : `准备生成 ${selectedQuantity} 张图片`;

            // 显示居中状态提示
            showCenterStatus('🎨', initialMessage, '正在初始化...', 0);

            try {
                await processGenerationQueue();
            } catch (error) {
                if (error.name === 'AbortError') {
                    console.log('生成已被用户取消');
                    showStatus('warning', '⏹️', '生成已取消', '用户主动取消了生成任务');
                    showCenterStatus('⏹️', '生成已取消', '用户主动取消了生成任务', 0);
                    setTimeout(() => {
                        hideCenterStatus();
                    }, 2000);
                } else {
                    console.error('生成过程中出现错误:', error);
                    showStatus('error', '❌', '生成失败', error.message);
                    showCenterStatus('❌', '生成失败', error.message, 0);
                    setTimeout(() => {
                        hideCenterStatus();
                    }, 3000);
                }
            } finally {
                isGenerating = false;
                document.querySelector('.generate-btn').disabled = false;
                currentGenerationController = null;
            }
        }

        async function processGenerationQueue() {
            const results = [];

            for (let i = 0; i < generationQueue.length; i++) {
                // 检查是否被取消
                if (currentGenerationController?.signal.aborted) {
                    throw new Error('Generation cancelled');
                }

                const task = generationQueue[i];
                const queueProgress = Math.round((i / generationQueue.length) * 100);
                updateCenterProgress(queueProgress, `正在生成第 ${task.index} 张图片...`, `队列进度: ${i + 1}/${generationQueue.length}`);

                try {
                    // 调用生成API，包含上传的图片
                    const response = await fetch('/api/generate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            prompt: task.prompt,
                            aspect_ratio: task.aspect_ratio,
                            image_urls: uploadedImageUrls.map(img => img.url) // 传递上传的图片URL
                        }),
                        signal: currentGenerationController?.signal
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const result = await response.json();

                    if (result.success) {
                        // 轮询任务状态
                        const imageUrl = await pollTaskStatusForBatch(result.task_id, task.index);

                        // 保存到历史记录
                        const historyItem = {
                            id: task.id,
                            url: imageUrl,
                            prompt: task.prompt,
                            aspect_ratio: task.aspect_ratio,
                            timestamp: new Date().toISOString(),
                            taskId: result.task_id,
                            hasUploadedImage: uploadedImageUrls.length > 0 // 标记是否使用了上传图片
                        };

                        generationHistory.unshift(historyItem);
                        results.push(historyItem);

                    } else {
                        throw new Error(result.error || '生成失败');
                    }
                } catch (error) {
                    if (error.name === 'AbortError') {
                        throw error; // 重新抛出取消错误
                    }
                    console.error(`第 ${task.index} 张图片生成失败:`, error);
                    updateProgressModal(`第 ${task.index} 张图片生成失败: ${error.message}`,
                                      `继续处理剩余图片...`);
                    await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒后继续
                }
            }

            // 保存历史记录到本地存储
            localStorage.setItem('generationHistory', JSON.stringify(generationHistory));
            updateHistoryDisplay();

            if (results.length > 0) {
                const hasImages = uploadedImageUrls.length > 0;
                const successMessage = hasImages
                    ? `成功生成 ${results.length} 张图片（基于上传图片和提示词）`
                    : `成功生成 ${results.length} 张图片`;

                showStatus('success', '✅', '生成完成！', successMessage);
                showCenterStatus('✅', '生成完成！', successMessage, 100);

                // 显示结果
                displayResults(results);

                // 1秒后隐藏居中提示
                setTimeout(() => {
                    hideCenterStatus();
                    hideStatus();
                }, 1000);
            } else {
                showStatus('error', '❌', '生成失败', '所有图片生成都失败了，请检查网络连接或稍后重试');
                showCenterStatus('❌', '生成失败', '所有图片生成都失败了', 0);
                setTimeout(() => {
                    hideCenterStatus();
                }, 3000);
            }
        }

        async function pollTaskStatusForBatch(taskId, imageIndex) {
            const maxAttempts = 60; // 最多轮询60次 (5分钟)
            let attempts = 0;

            return new Promise((resolve, reject) => {
                const poll = async () => {
                    try {
                        // 检查是否被取消
                        if (currentGenerationController?.signal.aborted) {
                            reject(new Error('Generation cancelled'));
                            return;
                        }

                        attempts++;

                        const response = await fetch(`/api/status/${taskId}`, {
                            signal: currentGenerationController?.signal
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const status = await response.json();

                        if (status.error) {
                            throw new Error(status.error);
                        }

                        // 更新进度
                        const progress = status.progress || 0;
                        updateCenterProgress(progress, `正在生成第 ${imageIndex} 张图片... ${progress}%`, `任务ID: ${taskId.substring(0, 8)}...`);

                        if (status.status === 'completed' && status.result_url) {
                            updateCenterProgress(100, `第 ${imageIndex} 张图片生成完成`, '正在处理下一张...');
                            resolve(status.result_url);

                        } else if (status.status === 'error') {
                            throw new Error('图像生成失败');

                        } else if (attempts >= maxAttempts) {
                            throw new Error('生成超时，请稍后重试');

                        } else {
                            // 继续轮询
                            setTimeout(poll, 3000); // 每3秒轮询一次
                        }

                    } catch (error) {
                        if (error.name === 'AbortError') {
                            reject(new Error('Generation cancelled'));
                        } else {
                            reject(error);
                        }
                    }
                };

                // 开始轮询
                setTimeout(poll, 2000); // 2秒后开始第一次轮询
            });
        }

        function showProgressModal() {
            const modal = document.getElementById('progress-modal');
            modal.style.display = 'flex';
            // 重置进度条
            updateProgressBar(0);
        }

        function hideProgressModal() {
            const modal = document.getElementById('progress-modal');
            modal.style.display = 'none';
        }

        function updateProgressModal(info, queue) {
            document.getElementById('modal-progress-info').textContent = info;
            document.getElementById('modal-progress-queue').textContent = queue || '';
        }

        function updateProgressBar(progress) {
            const progressBar = document.getElementById('modal-progress-fill');
            progressBar.style.width = Math.min(100, Math.max(0, progress)) + '%';
        }

        // 内联进度条控制函数
        function showInlineProgress() {
            document.getElementById('inline-progress').style.display = 'block';
            updateInlineProgress(0, '正在准备生成...', '');
        }

        function hideInlineProgress() {
            document.getElementById('inline-progress').style.display = 'none';
        }

        function updateInlineProgress(progress, text, details) {
            document.getElementById('progress-fill').style.width = Math.min(100, Math.max(0, progress)) + '%';
            document.getElementById('progress-text').textContent = text;
            document.getElementById('progress-details').textContent = details;
        }

        // 居中状态提示控制函数
        function showCenterStatus(icon, text, details, progress = 0) {
            const centerStatus = document.getElementById('generation-center-status');
            document.getElementById('center-status-icon').innerHTML = icon;
            document.getElementById('center-status-text').textContent = text;
            document.getElementById('center-status-details').textContent = details;
            document.getElementById('center-progress-fill').style.width = Math.min(100, Math.max(0, progress)) + '%';
            centerStatus.style.display = 'block';
        }

        function hideCenterStatus() {
            document.getElementById('generation-center-status').style.display = 'none';
        }

        function updateCenterProgress(progress, text, details) {
            document.getElementById('center-progress-fill').style.width = Math.min(100, Math.max(0, progress)) + '%';
            document.getElementById('center-status-text').textContent = text;
            document.getElementById('center-status-details').textContent = details;
        }

        function handleProgressOverlayClick(event) {
            // 只有点击遮罩层时才关闭，点击内容区域不关闭
            if (event.target === event.currentTarget) {
                if (confirm('确定要取消生成吗？')) {
                    cancelGeneration();
                }
            }
        }

        function cancelGeneration() {
            if (isGenerating && currentGenerationController) {
                currentGenerationController.abort();
                updateProgressModal('正在取消...', '请稍候');
            }
        }

        // 状态提示函数
        function showStatus(type, icon, text, details = '') {
            const statusDiv = document.getElementById('generation-status');
            const statusIcon = document.getElementById('status-icon');
            const statusText = document.getElementById('status-text');
            const statusDetails = document.getElementById('status-details');

            // 清除之前的样式类
            statusDiv.className = '';
            statusDiv.classList.add(`status-${type}`);

            statusIcon.innerHTML = icon;
            statusText.textContent = text;
            statusDetails.textContent = details;

            statusDiv.style.display = 'block';
        }

        function hideStatus() {
            document.getElementById('generation-status').style.display = 'none';
        }

        function showImageAssociationInfo() {
            const hasImages = uploadedImageUrls.length > 0;
            const prompt = document.querySelector('.prompt-area').value.trim();

            if (hasImages && prompt) {
                showStatus('info', '🎨', '图片与提示词已关联',
                    `将基于上传的 ${uploadedImageUrls.length} 张图片和提示词"${prompt.substring(0, 30)}${prompt.length > 30 ? '...' : ''}"进行生成`);
            } else if (hasImages && !prompt) {
                showStatus('warning', '📷', '仅使用上传图片生成',
                    `将基于上传的 ${uploadedImageUrls.length} 张图片进行生成，建议添加提示词以获得更好效果`);
            } else if (!hasImages && prompt) {
                showStatus('info', '✨', '使用提示词生成',
                    `将基于提示词"${prompt.substring(0, 30)}${prompt.length > 30 ? '...' : ''}"进行生成`);
            }
        }

        function displayResults(results) {
            if (results.length === 0) return;

            let html = '';
            if (results.length === 1) {
                // 单张图片显示
                const result = results[0];
                html = `
                    <img src="${result.url}" alt="Generated Image" class="sample-image"
                         style="cursor: pointer;"
                         onclick="viewImage('${result.url}', '${result.prompt}', '${result.timestamp}')"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRkY2Mzc0Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTUwIiBmaWxsPSIjRkZGRkZGIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiPuWbvuWDj+WKoOi9veWksei0pTwvdGV4dD4KPC9zdmc+'">
                    <p style="margin-top: 15px; color: rgba(255,255,255,0.7);">基于提示词: "${result.prompt}"</p>
                    <p style="margin-top: 5px; color: rgba(255,255,255,0.5); font-size: 12px;">点击图片放大预览</p>
                    <div style="margin-top: 15px; display: flex; gap: 10px; justify-content: center;">
                        <button class="result-btn" onclick="downloadImage('${result.url}', '${result.id}', this)">
                            <i class="fas fa-download"></i> 下载
                        </button>
                        <button class="result-btn" onclick="viewImage('${result.url}', '${result.prompt}', '${result.timestamp}')">
                            <i class="fas fa-search-plus"></i> 预览
                        </button>
                    </div>
                `;
            } else {
                // 多张图片网格显示
                html = '<div class="result-grid">';
                results.forEach((result, index) => {
                    html += `
                        <div class="result-item">
                            <img src="${result.url}" alt="Generated Image ${index + 1}" class="result-image"
                                 style="cursor: pointer;"
                                 onclick="viewImage('${result.url}', '${result.prompt}', '${result.timestamp}')"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRkY2Mzc0Ii8+Cjx0ZXh0IHg9IjEwMCIgeT0iNzUiIGZpbGw9IiNGRkZGRkYiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5Zu+5YOP5Yqg6L295aSx6LSlPC90ZXh0Pgo8L3N2Zz4='">
                            <div class="result-actions">
                                <button class="result-btn" onclick="downloadImage('${result.url}', '${result.id}', this)">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="result-btn" onclick="viewImage('${result.url}', '${result.prompt}', '${result.timestamp}')">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                html += `
                    <div style="margin-top: 20px; text-align: center;">
                        <p style="color: rgba(255,255,255,0.7); margin-bottom: 10px;">
                            成功生成 ${results.length} 张图片
                        </p>
                        <button class="result-btn" onclick="downloadAllResults(this)" style="margin-right: 10px;">
                            <i class="fas fa-download"></i> 下载全部
                        </button>
                    </div>
                `;
            }

            document.getElementById('result-content').innerHTML = html;
        }

        // 历史记录管理函数
        function updateHistoryDisplay() {
            const historyList = document.getElementById('history-list');
            const historyCount = document.getElementById('history-count');

            historyCount.textContent = generationHistory.length;

            if (generationHistory.length === 0) {
                historyList.innerHTML = `
                    <div class="no-tasks">
                        <i class="fas fa-image" style="font-size: 24px; margin-bottom: 10px; opacity: 0.5;"></i>
                        <p>暂无历史记录</p>
                    </div>
                `;
                return;
            }

            let html = '';
            generationHistory.forEach((item, index) => {
                const date = new Date(item.timestamp).toLocaleString('zh-CN');
                html += `
                    <div class="history-item" data-id="${item.id}">
                        <input type="checkbox" class="history-checkbox" data-id="${item.id}">
                        <img src="${item.url}" alt="Generated Image" class="history-image"
                             onclick="viewImage('${item.url}', '${item.prompt}', '${item.timestamp}')"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRkY2Mzc0Ii8+Cjx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZpbGw9IiNGRkZGRkYiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMCIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5Zu+5YOP5Yqg6L295aSx6LSlPC90ZXh0Pgo8L3N2Zz4='">
                        <div class="history-info">
                            <div style="font-weight: 500; margin-bottom: 3px;">${item.prompt.substring(0, 30)}${item.prompt.length > 30 ? '...' : ''}</div>
                            <div>宽高比: ${item.aspect_ratio} | ${date} ${item.hasUploadedImage ? '| 📷 含上传图片' : ''}</div>
                        </div>
                        <div class="history-actions">
                            <button class="history-action-btn" onclick="downloadImage('${item.url}', '${item.id}', this)">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="history-action-btn" onclick="viewImage('${item.url}', '${item.prompt}', '${item.timestamp}')">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button class="history-action-btn danger" onclick="deleteHistoryItem('${item.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            });

            historyList.innerHTML = html;
        }

        function selectAllHistory() {
            const checkboxes = document.querySelectorAll('.history-checkbox');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);

            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
                const item = cb.closest('.history-item');
                if (cb.checked) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            });
        }

        function getSelectedHistoryItems() {
            const selected = [];
            document.querySelectorAll('.history-checkbox:checked').forEach(cb => {
                const id = cb.dataset.id;
                const item = generationHistory.find(h => h.id == id);
                if (item) selected.push(item);
            });
            return selected;
        }

        function downloadSelected() {
            const selected = getSelectedHistoryItems();
            if (selected.length === 0) {
                showStatus('warning', '⚠️', '请先选择要下载的图片', '请勾选要下载的图片');
                setTimeout(hideStatus, 2000);
                return;
            }

            selected.forEach(item => {
                downloadImage(item.url, item.id, null);
            });
        }

        function deleteSelected() {
            const selected = getSelectedHistoryItems();
            if (selected.length === 0) {
                alert('请先选择要删除的图片');
                return;
            }

            if (confirm(`确定要删除选中的 ${selected.length} 张图片吗？`)) {
                selected.forEach(item => {
                    const index = generationHistory.findIndex(h => h.id === item.id);
                    if (index > -1) {
                        generationHistory.splice(index, 1);
                    }
                });

                localStorage.setItem('generationHistory', JSON.stringify(generationHistory));
                updateHistoryDisplay();
            }
        }

        function deleteHistoryItem(id) {
            if (confirm('确定要删除这张图片吗？')) {
                const index = generationHistory.findIndex(h => h.id == id);
                if (index > -1) {
                    generationHistory.splice(index, 1);
                    localStorage.setItem('generationHistory', JSON.stringify(generationHistory));
                    updateHistoryDisplay();
                }
            }
        }

        // 下载和查看函数
        async function downloadImage(url, filename, buttonElement = null) {
            try {
                // 显示下载提示
                const originalText = buttonElement?.textContent;
                if (buttonElement) {
                    buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 下载中...';
                    buttonElement.disabled = true;
                }

                console.log('开始下载图片:', url);

                // 尝试多种下载方法
                let success = false;

                // 方法1: 对于Cloudflare R2等签名URL，直接使用fetch方法
                // 跳过直接下载，因为签名URL通常有CORS限制

                // 方法1: 使用后端代理下载（优先使用，因为签名URL通常有CORS限制）
                try {
                    console.log('尝试代理下载...');
                    const proxyResponse = await fetch('/api/download-image', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ url: url })
                    });

                    if (proxyResponse.ok) {
                        const blob = await proxyResponse.blob();
                        const downloadUrl = window.URL.createObjectURL(blob);

                        const a = document.createElement('a');
                        a.href = downloadUrl;
                        a.download = `generated_image_${filename || Date.now()}.jpg`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);

                        window.URL.revokeObjectURL(downloadUrl);
                        success = true;
                        console.log('代理下载成功');
                    } else {
                        throw new Error(`代理下载失败: ${proxyResponse.status}`);
                    }
                } catch (proxyError) {
                    console.log('代理下载失败，尝试直接fetch:', proxyError);
                }

                // 方法2: 如果代理失败，尝试直接fetch
                if (!success) {
                    try {
                        console.log('尝试直接fetch下载...');
                        const response = await fetch(url, {
                            mode: 'cors',
                            credentials: 'omit',
                            headers: {
                                'Accept': 'image/*,*/*;q=0.8'
                            }
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const blob = await response.blob();
                        const downloadUrl = window.URL.createObjectURL(blob);

                        const a = document.createElement('a');
                        a.href = downloadUrl;
                        a.download = `generated_image_${filename || Date.now()}.jpg`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);

                        window.URL.revokeObjectURL(downloadUrl);
                        success = true;
                        console.log('直接fetch下载成功');
                    } catch (fetchError) {
                        console.log('直接fetch下载失败:', fetchError);
                    }
                }

                // 方法3: 如果前面都失败，尝试简单的直接下载
                if (!success) {
                    try {
                        console.log('尝试简单直接下载...');
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `generated_image_${filename || Date.now()}.jpg`;
                        a.target = '_blank';
                        a.rel = 'noopener noreferrer';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);

                        // 对于直接下载，我们假设它成功了
                        success = true;
                        console.log('简单直接下载已触发');
                    } catch (directError) {
                        console.log('简单直接下载失败:', directError);
                    }
                }

                if (!success) {
                    // 最后的备用方案：在新窗口打开图片
                    window.open(url, '_blank');
                    showStatus('info', 'ℹ️', '已在新窗口打开图片', '请右键保存图片到本地');
                } else {
                    showStatus('success', '✅', '下载成功', '图片已保存到下载文件夹');
                }

                // 恢复按钮状态
                if (buttonElement) {
                    buttonElement.innerHTML = originalText || '<i class="fas fa-download"></i>';
                    buttonElement.disabled = false;
                }

            } catch (error) {
                console.error('下载过程出错:', error);
                showStatus('error', '❌', '下载失败', '请尝试右键保存图片或检查网络连接');
                setTimeout(hideStatus, 3000);

                // 恢复按钮状态
                if (buttonElement) {
                    buttonElement.innerHTML = '<i class="fas fa-download"></i>';
                    buttonElement.disabled = false;
                }
            }
        }

        function downloadAllResults(buttonElement) {
            if (buttonElement) {
                buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 下载中...';
                buttonElement.disabled = true;
            }

            const images = document.querySelectorAll('.result-image, .sample-image');
            let completed = 0;

            images.forEach((img, index) => {
                setTimeout(() => {
                    downloadImage(img.src, `batch_${Date.now()}_${index + 1}`, null);
                    completed++;

                    if (completed === images.length && buttonElement) {
                        buttonElement.innerHTML = '<i class="fas fa-download"></i> 下载全部';
                        buttonElement.disabled = false;
                    }
                }, index * 500); // 间隔500ms下载，避免同时下载太多
            });
        }

        function viewImage(url, prompt = '', timestamp = '') {
            showImagePreview(url, prompt, timestamp);
        }

        // 图片预览相关函数
        let currentPreviewUrl = '';
        let currentPreviewPrompt = '';
        let currentPreviewTimestamp = '';

        function showImagePreview(url, prompt = '', timestamp = '') {
            currentPreviewUrl = url;
            currentPreviewPrompt = prompt;
            currentPreviewTimestamp = timestamp;

            const modal = document.getElementById('image-preview-modal');
            const img = document.getElementById('preview-image');
            const info = document.getElementById('preview-info');

            img.src = url;
            img.classList.remove('zoomed');

            // 设置图片信息
            let infoText = '';
            if (prompt) {
                infoText += `提示词: ${prompt}`;
            }
            if (timestamp) {
                const date = new Date(timestamp).toLocaleString('zh-CN');
                infoText += infoText ? ` | 生成时间: ${date}` : `生成时间: ${date}`;
            }
            info.textContent = infoText;

            modal.style.display = 'flex';

            // 添加键盘事件监听
            document.addEventListener('keydown', handlePreviewKeydown);
        }

        function closeImagePreview() {
            const modal = document.getElementById('image-preview-modal');
            modal.style.display = 'none';

            // 移除键盘事件监听
            document.removeEventListener('keydown', handlePreviewKeydown);
        }

        function toggleImageZoom() {
            const img = document.getElementById('preview-image');
            img.classList.toggle('zoomed');
        }

        function downloadPreviewImage() {
            if (currentPreviewUrl) {
                const filename = `preview_${Date.now()}`;
                const button = event?.target;
                downloadImage(currentPreviewUrl, filename, button);
            }
        }

        function openImageInNewTab() {
            if (currentPreviewUrl) {
                window.open(currentPreviewUrl, '_blank');
            }
        }

        async function copyImageUrl() {
            if (currentPreviewUrl) {
                try {
                    await navigator.clipboard.writeText(currentPreviewUrl);
                    showStatus('success', '📋', '链接已复制', '图片链接已复制到剪贴板');
                    setTimeout(hideStatus, 2000);
                } catch (error) {
                    console.error('复制失败:', error);
                    showStatus('error', '❌', '复制失败', '无法复制到剪贴板，请手动复制');
                    setTimeout(hideStatus, 2000);
                }
            }
        }

        function handlePreviewKeydown(event) {
            switch(event.key) {
                case 'Escape':
                    closeImagePreview();
                    break;
                case ' ':
                case 'Enter':
                    event.preventDefault();
                    toggleImageZoom();
                    break;
                case 'd':
                case 'D':
                    event.preventDefault();
                    downloadPreviewImage();
                    break;
                case 'o':
                case 'O':
                    event.preventDefault();
                    openImageInNewTab();
                    break;
                case 'c':
                case 'C':
                    event.preventDefault();
                    copyImageUrl();
                    break;
            }
        }

        // 历史记录选择状态管理
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('history-checkbox')) {
                const item = e.target.closest('.history-item');
                if (e.target.checked) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            }
        });
    </script>
</body>
</html>
