#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试水印去除API连接
"""

import asyncio
import httpx
from proxy_config import get_proxy_config

async def test_watermark_api():
    """测试水印去除API是否可访问"""
    print("🧪 测试水印去除API连接")
    print("=" * 50)
    
    proxies = get_proxy_config()
    proxy_url = proxies.get("https") or proxies.get("http")
    
    print(f"📡 使用代理: {proxy_url}")
    
    # 测试API端点
    test_url = "https://api.watermarkremover.io"
    
    try:
        print(f"🌐 测试连接: {test_url}")
        
        async with httpx.AsyncClient(proxy=proxy_url, timeout=10) as client:
            response = await client.get(test_url)
            
        print(f"✅ 连接成功 - 状态码: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("🎉 水印去除API可正常访问!")
            return True
        else:
            print(f"⚠️ API返回非200状态码: {response.status_code}")
            return False
            
    except httpx.ConnectTimeout:
        print("⏰ 连接超时")
        return False
    except httpx.ProxyError as e:
        print(f"❌ 代理错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

async def test_kontextflux_api():
    """测试KontextFlux API连接"""
    print("\n🧪 测试KontextFlux API连接")
    print("=" * 50)
    
    proxies = get_proxy_config()
    proxy_url = proxies.get("https") or proxies.get("http")
    
    test_url = "https://api.kontextflux.com/client/common/getConfig"
    
    try:
        print(f"🌐 测试连接: {test_url}")
        
        payload = {"token": None, "referrer": ""}
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Content-Type": "application/json",
            "Origin": "https://kontextflux.com",
            "Referer": "https://kontextflux.com/",
        }
        
        async with httpx.AsyncClient(proxy=proxy_url, timeout=10) as client:
            response = await client.post(test_url, json=payload, headers=headers)
            
        print(f"✅ 连接成功 - 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📊 响应数据: {data}")
            print("🎉 KontextFlux API可正常访问!")
            return True
        else:
            print(f"⚠️ API返回非200状态码: {response.status_code}")
            print(f"📄 响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 API连接测试工具")
    print("=" * 60)
    
    # 测试水印去除API
    watermark_ok = await test_watermark_api()
    
    # 测试KontextFlux API
    kontextflux_ok = await test_kontextflux_api()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"   水印去除API: {'✅ 可访问' if watermark_ok else '❌ 不可访问'}")
    print(f"   KontextFlux API: {'✅ 可访问' if kontextflux_ok else '❌ 不可访问'}")
    
    if watermark_ok and kontextflux_ok:
        print("\n🎉 所有API都可正常访问!")
        print("💡 水印去除功能应该能正常工作")
    else:
        print("\n⚠️ 部分API无法访问，请检查:")
        print("   1. V2Ray代理是否正常运行")
        print("   2. 网络连接是否稳定")
        print("   3. 防火墙设置")

if __name__ == "__main__":
    asyncio.run(main())
