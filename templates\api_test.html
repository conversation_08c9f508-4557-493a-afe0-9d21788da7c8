<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KontextFlux API 测试</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .panel {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            grid-column: 1 / -1;
        }

        .header h1 {
            font-size: 2rem;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 12px;
            color: #ffffff;
            font-size: 14px;
            font-family: inherit;
        }

        .form-control:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 120px;
            font-family: 'Courier New', monospace;
        }

        .btn {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 24px;
            color: #ffffff;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .response-area {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .info-box {
            background: rgba(99, 102, 241, 0.1);
            border: 1px solid rgba(99, 102, 241, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-box h3 {
            margin-bottom: 10px;
            color: #6366f1;
        }

        .info-box code {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }

        .nav-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #6366f1;
            text-decoration: none;
            font-weight: 500;
        }

        .nav-link:hover {
            color: #8b5cf6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="/" class="nav-link"><i class="fas fa-arrow-left"></i> 返回主页</a>
            <h1><i class="fas fa-code"></i> API 测试工具</h1>
            <p>测试 KontextFlux OpenAI 兼容 API</p>
        </div>

        <!-- 请求面板 -->
        <div class="panel">
            <div class="info-box">
                <h3><i class="fas fa-info-circle"></i> API 信息</h3>
                <p><strong>端点:</strong> <code>/v1/chat/completions</code></p>
                <p><strong>模型:</strong> <code>kontext-flux</code></p>
                <p><strong>密钥:</strong> <code>sk-dummy-1f55e1bd4c3b4849bed71334258cbdf2</code></p>
            </div>

            <div class="form-group">
                <label for="api-key">API 密钥</label>
                <input type="text" id="api-key" class="form-control" 
                       value="sk-dummy-1f55e1bd4c3b4849bed71334258cbdf2">
            </div>

            <div class="form-group">
                <label for="model">模型</label>
                <input type="text" id="model" class="form-control" value="kontext-flux">
            </div>

            <div class="form-group">
                <label for="aspect-ratio">宽高比</label>
                <select id="aspect-ratio" class="form-control">
                    <option value="auto">Auto (自动)</option>
                    <option value="1:1">1:1 (正方形)</option>
                    <option value="2:3">2:3 (竖屏)</option>
                    <option value="3:2">3:2 (横屏)</option>
                    <option value="9:16">9:16 (手机竖屏)</option>
                    <option value="16:9">16:9 (宽屏)</option>
                </select>
            </div>

            <div class="form-group">
                <label for="prompt">提示词</label>
                <textarea id="prompt" class="form-control"
                          placeholder="输入图像描述...">一只戴着VR眼镜的企鹅在酒吧里喝啤酒</textarea>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="stream" style="margin-right: 8px;">
                    流式响应
                </label>
            </div>

            <button class="btn" onclick="testAPI()">
                <i class="fas fa-play"></i> 发送请求
            </button>
        </div>

        <!-- 响应面板 -->
        <div class="panel">
            <h3><i class="fas fa-terminal"></i> 响应结果</h3>
            <div id="response" class="response-area">等待请求...</div>
        </div>
    </div>

    <script>
        async function testAPI() {
            const apiKey = document.getElementById('api-key').value;
            const model = document.getElementById('model').value;
            const prompt = document.getElementById('prompt').value;
            const aspectRatio = document.getElementById('aspect-ratio').value;
            const stream = document.getElementById('stream').checked;
            const responseArea = document.getElementById('response');

            if (!prompt.trim()) {
                alert('请输入提示词');
                return;
            }

            const requestData = {
                model: model,
                messages: [
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                stream: stream,
                aspect_ratio: aspectRatio
            };

            responseArea.textContent = '发送请求中...\n\n请求数据:\n' + JSON.stringify(requestData, null, 2);

            try {
                const response = await fetch('/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify(requestData)
                });

                responseArea.textContent += '\n\n响应状态: ' + response.status + ' ' + response.statusText;
                responseArea.textContent += '\n响应头:\n' + JSON.stringify(Object.fromEntries(response.headers), null, 2);

                if (stream) {
                    responseArea.textContent += '\n\n流式响应:\n';
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        const chunk = decoder.decode(value);
                        responseArea.textContent += chunk;
                        responseArea.scrollTop = responseArea.scrollHeight;
                    }
                } else {
                    const result = await response.json();
                    responseArea.textContent += '\n\n响应数据:\n' + JSON.stringify(result, null, 2);
                }

            } catch (error) {
                responseArea.textContent += '\n\n错误: ' + error.message;
            }

            responseArea.scrollTop = responseArea.scrollHeight;
        }
    </script>
</body>
</html>
