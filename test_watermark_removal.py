#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水印去除功能测试脚本
"""

import asyncio
import os
import sys
from kontextflux2api import downloadFile, watermarkremover

async def test_watermark_removal():
    """测试水印去除功能"""
    print("🧪 水印去除功能测试")
    print("=" * 50)
    
    # 测试图片URL (可以替换为实际的KontextFlux生成的图片URL)
    test_image_url = "https://via.placeholder.com/512x512.png?text=Test+Image+With+Watermark"
    
    print(f"📷 测试图片URL: {test_image_url}")
    
    # 步骤1: 下载图片
    print("\n🔄 步骤1: 下载测试图片...")
    filename = await downloadFile(test_image_url, "test_image.png")
    
    if not filename:
        print("❌ 图片下载失败，测试终止")
        return False
        
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return False
        
    print(f"✅ 图片下载成功: {filename}")
    print(f"📊 文件大小: {os.path.getsize(filename)} bytes")
    
    # 步骤2: 水印去除
    print("\n🔄 步骤2: 执行水印去除...")
    try:
        result_url = await watermarkremover(filename)
        
        if result_url:
            print(f"🎉 水印去除成功!")
            print(f"🔗 结果URL: {result_url}")
            
            # 验证结果URL是否可访问
            print("\n🔄 步骤3: 验证结果URL...")
            test_filename = await downloadFile(result_url, "watermark_removed.png")
            
            if test_filename and os.path.exists(test_filename):
                print(f"✅ 结果图片下载成功: {test_filename}")
                print(f"📊 处理后文件大小: {os.path.getsize(test_filename)} bytes")
                
                # 清理测试文件
                try:
                    os.remove(test_filename)
                    print(f"🗑️ 清理测试文件: {test_filename}")
                except:
                    pass
                    
                return True
            else:
                print("❌ 结果图片下载失败")
                return False
        else:
            print("❌ 水印去除失败")
            return False
            
    except Exception as e:
        print(f"❌ 水印去除过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理原始测试文件
        try:
            if os.path.exists(filename):
                os.remove(filename)
                print(f"🗑️ 清理原始文件: {filename}")
        except:
            pass

async def test_with_real_kontextflux_image():
    """使用真实的KontextFlux图片测试"""
    print("\n🔄 真实KontextFlux图片测试")
    print("=" * 50)
    
    # 这里需要一个真实的KontextFlux生成的图片URL
    # 你可以先生成一张图片，然后把URL放在这里测试
    real_image_url = input("请输入KontextFlux生成的图片URL (或按Enter跳过): ").strip()
    
    if not real_image_url:
        print("⏭️ 跳过真实图片测试")
        return True
        
    print(f"📷 真实图片URL: {real_image_url}")
    
    # 下载并处理
    filename = await downloadFile(real_image_url, "real_kontextflux_image.png")
    
    if not filename:
        print("❌ 真实图片下载失败")
        return False
        
    try:
        result_url = await watermarkremover(filename)
        
        if result_url:
            print(f"🎉 真实图片水印去除成功!")
            print(f"🔗 结果URL: {result_url}")
            print("💡 你可以在浏览器中打开这个URL查看结果")
            return True
        else:
            print("❌ 真实图片水印去除失败")
            return False
            
    except Exception as e:
        print(f"❌ 真实图片处理出错: {e}")
        return False

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    try:
        import httpx
        print("✅ httpx 已安装")
    except ImportError:
        print("❌ httpx 未安装")
        return False
        
    try:
        from proxy_config import get_proxy_config
        proxies = get_proxy_config()
        print(f"✅ 代理配置: {proxies}")
    except ImportError:
        print("⚠️ 代理配置文件不存在，将使用直连")
    except Exception as e:
        print(f"⚠️ 代理配置错误: {e}")
        
    return True

async def main():
    """主函数"""
    print("🚀 KontextFlux 水印去除测试工具")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败")
        return
        
    # 基础功能测试
    success1 = await test_watermark_removal()
    
    # 真实图片测试
    success2 = await test_with_real_kontextflux_image()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"   基础功能测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"   真实图片测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1:
        print("\n🎉 水印去除功能正常工作!")
        print("💡 现在可以启动服务器测试完整流程")
    else:
        print("\n⚠️ 水印去除功能存在问题，请检查:")
        print("   1. 网络连接是否正常")
        print("   2. 代理配置是否正确")
        print("   3. watermarkremover.io 服务是否可用")

if __name__ == "__main__":
    asyncio.run(main())
