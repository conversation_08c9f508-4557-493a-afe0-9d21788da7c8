@echo off
chcp 65001 >nul
title KontextFlux API Server
color 0A
cls

echo.
echo ==========================================
echo     KontextFlux API Server (Windows)
echo ==========================================
echo.

REM Check Python
echo [1/4] Checking Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo X Python not found, please install Python first
    echo Download: https://www.python.org/downloads/
    goto :end
)
echo + Python is installed

REM Check script file
echo [2/4] Checking script file...
if not exist "kontextfluxflaskapi.py" (
    echo X Script file kontextfluxflaskapi.py not found
    goto :end
)
echo + Script file exists

REM Install dependencies
echo [3/4] Installing dependencies...
pip install flask requests httpx websocket-client pycryptodome --quiet --disable-pip-version-check
if %errorlevel% neq 0 (
    echo X Failed to install dependencies
    goto :end
)
echo + Dependencies installed

REM Start server
echo [4/4] Starting server...
echo.
echo ==========================================
echo Starting server...
echo Address: http://127.0.0.1:8002
echo Press Ctrl+C to stop
echo ==========================================
echo.

python kontextfluxflaskapi.py

:end
echo.
echo ==========================================
echo Program finished
echo ==========================================
echo.
pause
