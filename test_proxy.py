#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V2Ray代理测试脚本
用于测试V2Ray代理是否正常工作
"""

import asyncio
import httpx
from proxy_config import get_proxy_config

async def test_proxy():
    """测试代理连接"""
    proxies = get_proxy_config()
    
    print("🔍 开始测试V2Ray代理连接...")
    print(f"📡 代理配置: {proxies}")
    
    # 测试网站列表
    test_urls = [
        "https://httpbin.org/ip",           # 显示IP地址
        "https://www.google.com",           # Google
        "https://api.kontextflux.com/client/common/getConfig",  # KontextFlux API
    ]
    
    for url in test_urls:
        print(f"\n🌐 测试连接: {url}")
        try:
            proxy_url = proxies.get("https") or proxies.get("http")
            async with httpx.AsyncClient(proxy=proxy_url, timeout=10) as client:
                response = await client.get(url)
                
                if response.status_code == 200:
                    print(f"✅ 连接成功 - 状态码: {response.status_code}")
                    
                    # 如果是IP检测网站，显示IP信息
                    if "httpbin.org/ip" in url:
                        try:
                            ip_info = response.json()
                            print(f"🌍 当前IP: {ip_info.get('origin', 'Unknown')}")
                        except:
                            pass
                else:
                    print(f"⚠️  连接异常 - 状态码: {response.status_code}")
                    
        except httpx.ConnectTimeout:
            print("❌ 连接超时 - 请检查V2Ray是否运行")
        except httpx.ProxyError as e:
            print(f"❌ 代理错误: {e}")
            print("💡 请检查:")
            print("   1. V2Ray客户端是否正在运行")
            print("   2. proxy_config.py中的端口配置是否正确")
            print("   3. V2Ray的入站连接设置")
        except Exception as e:
            print(f"❌ 连接失败: {e}")

def test_without_proxy():
    """测试不使用代理的连接"""
    print("\n🔍 测试直连（不使用代理）...")
    
    import requests
    try:
        response = requests.get("https://httpbin.org/ip", timeout=10)
        if response.status_code == 200:
            ip_info = response.json()
            print(f"✅ 直连成功 - IP: {ip_info.get('origin', 'Unknown')}")
        else:
            print(f"⚠️  直连异常 - 状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 直连失败: {e}")

def check_v2ray_ports():
    """检查V2Ray端口是否开放"""
    print("\n🔍 检查V2Ray端口状态...")
    
    import socket
    from proxy_config import V2RAY_SOCKS5_HOST, V2RAY_SOCKS5_PORT, V2RAY_HTTP_HOST, V2RAY_HTTP_PORT
    
    ports_to_check = [
        (V2RAY_SOCKS5_HOST, V2RAY_SOCKS5_PORT, "SOCKS5"),
        (V2RAY_HTTP_HOST, V2RAY_HTTP_PORT, "HTTP"),
    ]
    
    for host, port, protocol in ports_to_check:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"✅ {protocol}代理端口 {host}:{port} 开放")
            else:
                print(f"❌ {protocol}代理端口 {host}:{port} 未开放")
        except Exception as e:
            print(f"❌ 检查端口 {host}:{port} 时出错: {e}")

if __name__ == "__main__":
    print("🚀 V2Ray代理测试工具")
    print("=" * 50)
    
    # 检查端口状态
    check_v2ray_ports()
    
    # 测试直连
    test_without_proxy()
    
    # 测试代理连接
    asyncio.run(test_proxy())
    
    print("\n" + "=" * 50)
    print("📝 测试完成")
    print("\n💡 如果代理测试失败，请检查:")
    print("   1. V2Ray客户端是否正在运行")
    print("   2. proxy_config.py中的配置是否正确")
    print("   3. 防火墙是否阻止了连接")
    print("   4. V2Ray的入站连接配置")
